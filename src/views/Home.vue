<template>
  <div class="bg-light-bg-primary dark:bg-dark-bg-primary min-h-screen">
    <!-- Hero Section -->
    <HeroSection />

    <!-- Featured Categories -->
    <CategoriesSection />

    <!-- Featured Products -->
    <FeaturedProductsSection />

    <!-- Stats Section -->
    <StatsSection />

    <!-- Newsletter Section -->
    <NewsletterSection />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import {
  HeroSection,
  CategoriesSection,
  FeaturedProductsSection,
  StatsSection,
  NewsletterSection
} from '@/components/home'

onMounted(() => {
  console.log('🏠 Home page loaded successfully')
})
</script>


