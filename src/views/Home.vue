<template>
  <div class="min-h-screen">
    <!-- <PERSON> Section -->
    <HeroSection />

    <!-- Featured Categories -->
    <FeaturedCategoriesSection />

    <!-- Featured Products -->
    <FeaturedProductsSection />

    <!-- Stats Section -->
    <StatsSection />

    <!-- Newsletter Section -->
    <NewsletterSection />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import AOS from 'aos'
import {
  HeroSection,
  FeaturedCategoriesSection,
  FeaturedProductsSection,
  StatsSection,
  NewsletterSection
} from '@/components/home'

onMounted(() => {
  // Refresh AOS when component mounts
  AOS.refresh()
  console.log('🏠 Home page loaded successfully with AOS animations')
})
</script>


