<template>
  <div class="container mx-auto p-6">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-light-text-primary dark:text-dark-text-primary mb-2">
        Product Card Demo
      </h1>
      <p class="text-light-text-secondary dark:text-dark-text-secondary">
        Beautiful product cards inspired by modern e-commerce design
      </p>
    </div>

    <!-- Featured Products Section -->
    <section class="py-12 bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-xl">
      <div class="container mx-auto px-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 class="text-2xl md:text-3xl font-bold tracking-tight text-light-text-primary dark:text-dark-text-primary">
              Featured Products
            </h2>
            <p class="text-light-text-muted dark:text-dark-text-muted mt-2">
              Our most popular items chosen by athletes like you
            </p>
          </div>
          <Button variant="outline" class="mt-2 md:mt-0">
            View all products
          </Button>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <ProductCard
            v-for="product in products"
            :key="product.id"
            :product="product"
            @click="handleProductClick"
            @add-to-cart="handleAddToCart"
            @toggle-favorite="handleToggleFavorite"
            @quick-view="handleQuickView"
          />
        </div>
      </div>
    </section>

    <!-- Single Product Card Example -->
    <section class="py-12">
      <h2 class="text-2xl font-bold text-light-text-primary dark:text-dark-text-primary mb-6">
        Single Product Card
      </h2>
      <div class="max-w-sm">
        <ProductCard
          :product="products[0]"
          @click="handleProductClick"
          @add-to-cart="handleAddToCart"
          @toggle-favorite="handleToggleFavorite"
          @quick-view="handleQuickView"
        />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui'
import ProductCard from '@/components/examples/ProductCard.vue'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  category: string
  rating: number
  reviews?: number
  inStock: boolean
  isFavorite?: boolean
}

const products = ref<Product[]>([
  {
    id: '1',
    name: 'Giày chạy bộ Pro Running',
    price: 2990,
    rating: 4.8,
    reviews: 156,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Giày dép',
    inStock: true,
    isFavorite: false,
  },
  {
    id: '2',
    name: 'Bóng rổ Performance',
    price: 1290,
    originalPrice: 1790,
    rating: 4.7,
    reviews: 89,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Dụng cụ',
    inStock: true,
    isFavorite: true,
  },
  {
    id: '3',
    name: 'Áo thun Compression',
    price: 890,
    rating: 4.5,
    reviews: 234,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Quần áo',
    inStock: true,
    isFavorite: false,
  },
  {
    id: '4',
    name: 'Đồng hồ thông minh Fitness',
    price: 2290,
    originalPrice: 2990,
    rating: 4.9,
    reviews: 67,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Phụ kiện',
    inStock: true,
    isFavorite: true,
  },
  {
    id: '5',
    name: 'Thảm yoga cao cấp',
    price: 1190,
    rating: 4.6,
    reviews: 123,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Dụng cụ',
    inStock: false,
    isFavorite: false,
  },
  {
    id: '6',
    name: 'Bình nước thể thao',
    price: 490,
    originalPrice: 690,
    rating: 4.3,
    reviews: 345,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Phụ kiện',
    inStock: true,
    isFavorite: false,
  },
])

const handleProductClick = (product: Product) => {
  console.log('Product clicked:', product)
  // Navigate to product detail page
}

const handleAddToCart = (product: Product) => {
  console.log('Added to cart:', product)
  // Add to cart logic
}
</script>
