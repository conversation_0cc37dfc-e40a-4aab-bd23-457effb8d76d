<template>
  <section class="container-custom section-padding bg-light-bg-primary dark:bg-dark-bg-primary">
    <!-- Section Header -->
    <div
      class="text-center mb-12"
      data-aos="fade-up"
      data-aos-duration="600"
    >
      <h2 class="text-3xl lg:text-4xl font-bold text-light-text-primary dark:text-dark-text-primary mb-4">
        <PERSON><PERSON><PERSON> phẩm
        <span class="bg-gradient-to-r from-sport-primary to-sport-accent bg-clip-text text-transparent">
          Nổi bật
        </span>
      </h2>
      <p class="text-lg text-light-text-secondary dark:text-dark-text-secondary max-w-2xl mx-auto">
        Khám phá bộ sưu tập thiết bị thể thao và trang phục cao cấp đ<PERSON><PERSON>c tuyển chọn từ các thương hiệu hàng đầu
      </p>
    </div>

    <!-- Filter Tabs -->
    <div
      class="flex justify-center mb-8"
      data-aos="fade-up"
      data-aos-duration="600"
      data-aos-delay="200"
    >
      <div class="flex flex-wrap gap-2 p-1 bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-lg">
        <button
          v-for="filter in filters"
          :key="filter.value"
          :class="[
            'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
            activeFilter === filter.value
              ? 'bg-light-accent-sport dark:bg-dark-accent-sport text-white'
              : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-light-text-primary dark:hover:text-dark-text-primary'
          ]"
          @click="setActiveFilter(filter.value)"
        >
          {{ filter.label }}
        </button>
      </div>
    </div>

    <!-- Products Grid using ProductGrid component -->
    <div
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="400"
    >
      <ProductGrid
        :products="filteredProducts"
        :pagination="false"
        :show-filters="false"
        @product-click="handleProductClick"
        @add-to-cart="handleAddToCart"
        @toggle-favorite="handleToggleFavorite"
      />
    </div>

    <!-- View All Button -->
    <div
      class="text-center mt-12"
      data-aos="fade-up"
      data-aos-duration="600"
      data-aos-delay="600"
    >
      <button
        class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-sport-primary to-sport-accent text-white font-semibold rounded-lg hover:shadow-lg hover:scale-105 transition-all duration-300"
        @click="handleViewAllProducts"
      >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
        </svg>
        <span>Xem tất cả sản phẩm</span>
      </button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { BagOutline } from '@vicons/ionicons5'
import { ProductCard, generateSampleProducts } from '@/components/examples'
import type { Product } from '@/components/examples'

// Filter options
const filters = [
  { label: 'All', value: 'all' },
  { label: 'New Arrivals', value: 'new' },
  { label: 'Best Sellers', value: 'bestseller' },
  { label: 'On Sale', value: 'sale' }
]

// State
const activeFilter = ref('all')
const products = ref<Product[]>(generateSampleProducts(12))

// Computed
const filteredProducts = computed(() => {
  switch (activeFilter.value) {
    case 'new':
      return products.value.slice(0, 8) // Show first 8 as "new"
    case 'bestseller':
      return products.value.filter(p => p.rating >= 4).slice(0, 8)
    case 'sale':
      return products.value.filter(p => p.originalPrice && p.originalPrice > p.price).slice(0, 8)
    default:
      return products.value.slice(0, 8) // Show first 8 products
  }
})

// Methods
const setActiveFilter = (filter: string) => {
  activeFilter.value = filter
}

const handleProductClick = (product: Product) => {
  console.log('Navigate to product:', product.name)
  // Add navigation logic here
}

const handleAddToCart = (product: Product) => {
  console.log('Add to cart:', product.name)
  // Add cart logic here
}

const handleToggleFavorite = (product: Product) => {
  console.log('Toggle favorite:', product.name)
  // Add favorite logic here
  const index = products.value.findIndex(p => p.id === product.id)
  if (index !== -1) {
    products.value[index].isFavorite = !products.value[index].isFavorite
  }
}

const handleViewAllProducts = () => {
  console.log('Navigate to all products')
  // Add navigation logic here
}
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
