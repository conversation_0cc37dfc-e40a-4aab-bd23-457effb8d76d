# 🏠 Home Page Components

This directory contains all components specifically designed for the home page of the Sport Shop application.

## 📁 Components Overview

### 🎯 HeroSection.vue
- **Purpose**: Main hero banner with call-to-action
- **Features**: 
  - Gradient background with sports theme
  - Responsive design with image and content
  - Action buttons for navigation
  - Social proof indicators
  - Animated elements

### 🏷️ CategoriesSection.vue
- **Purpose**: Display featured sport categories
- **Features**:
  - Grid layout of category cards
  - Uses existing CategoryCard component
  - Animated entrance effects
  - "View All" button

### 🛍️ FeaturedProductsSection.vue
- **Purpose**: Showcase featured/popular products
- **Features**:
  - Filter tabs (All, New, Best Sellers, Sale)
  - Product grid using ProductCard component
  - Dynamic filtering
  - Add to cart and favorite functionality

### 📊 StatsSection.vue
- **Purpose**: Display company statistics and features
- **Features**:
  - Statistics cards with numbers and trends
  - Feature highlights with icons
  - Animated counters and progress indicators

### 📧 NewsletterSection.vue
- **Purpose**: Newsletter subscription form
- **Features**:
  - Email validation
  - Loading states
  - Success/error feedback
  - Gradient background with pattern
  - Benefits display

## 🎨 Design Principles

### Color Scheme
- **Light Mode**: Sport green (#00C897), sport red (#EF233C)
- **Dark Mode**: Neon green (#00E5A1), neon red (#FF4757)
- Consistent with overall theme

### Animations
- Fade-in effects with staggered delays
- Smooth transitions
- Gradient animations
- Hover effects

### Responsive Design
- Mobile-first approach
- Grid layouts that adapt to screen size
- Flexible typography scaling

## 🔧 Usage

```vue
<template>
  <div>
    <HeroSection />
    <CategoriesSection />
    <FeaturedProductsSection />
    <StatsSection />
    <NewsletterSection />
  </div>
</template>

<script setup lang="ts">
import {
  HeroSection,
  CategoriesSection,
  FeaturedProductsSection,
  StatsSection,
  NewsletterSection
} from '@/components/home'
</script>
```

## 🔗 Dependencies

### External Components
- Uses components from `@/components/examples`:
  - `CategoryCard`
  - `ProductCard`
  - `StatCard`

### UI Library
- Naive UI components (NButton, NInput, NIcon, etc.)
- Ionicons for icons

### Styling
- Tailwind CSS classes
- Custom CSS animations
- Sport-themed gradients and colors

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

Each component is optimized for all breakpoints with appropriate layout adjustments.

## 🎯 Performance Considerations

- Lazy loading for images
- Optimized animations
- Efficient re-rendering with Vue 3 Composition API
- Minimal bundle size impact

## 🔄 Future Enhancements

- A/B testing capabilities
- Dynamic content loading
- Personalization features
- Advanced analytics integration
