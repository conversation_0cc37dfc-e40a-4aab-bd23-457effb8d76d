// 🏠 Home Page Components Exports for Sport Shop

// Home Page Sections
export { default as HeroSection } from './HeroSection.vue'
export { default as CategoriesSection } from './CategoriesSection.vue'
export { default as FeaturedProductsSection } from './FeaturedProductsSection.vue'
export { default as StatsSection } from './StatsSection.vue'
export { default as NewsletterSection } from './NewsletterSection.vue'

// Component constants
export const HOME_COMPONENTS = {
  heroSection: 'HeroSection',
  categoriesSection: 'CategoriesSection',
  featuredProductsSection: 'FeaturedProductsSection',
  statsSection: 'StatsSection',
  newsletterSection: 'NewsletterSection',
} as const

// Home page configuration
export const HOME_CONFIG = {
  sections: [
    'hero',
    'categories',
    'featuredProducts',
    'stats',
    'newsletter'
  ],
  animations: {
    fadeInDelay: 0.1,
    slideUpDuration: 0.6,
    bounceDelay: 0.5
  }
} as const
