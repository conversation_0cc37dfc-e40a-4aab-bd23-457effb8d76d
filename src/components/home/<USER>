<template>
  <section class="py-16 bg-light-bg-primary dark:bg-dark-bg-primary">
    <div class="container-custom">
      <!-- Section Header -->
      <div 
        class="text-center mb-12"
        data-aos="fade-up"
        data-aos-duration="600"
      >
        <h2 class="text-3xl md:text-4xl font-bold text-light-text-primary dark:text-dark-text-primary mb-4">
          Shop by 
          <span class="bg-gradient-to-r from-sport-primary to-sport-accent bg-clip-text text-transparent">
            Category
          </span>
        </h2>
        <p class="text-lg text-light-text-secondary dark:text-dark-text-secondary max-w-2xl mx-auto">
          Discover our premium collection of sports equipment and gear, carefully curated for athletes and enthusiasts
        </p>
      </div>

      <!-- Categories Grid -->
      <div 
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        data-aos="fade-up"
        data-aos-duration="800"
        data-aos-delay="200"
      >
        <div
          v-for="(category, index) in categories"
          :key="category.id"
          :data-aos="'fade-up'"
          :data-aos-duration="600"
          :data-aos-delay="300 + (index * 100)"
        >
          <CategoryCard
            :category="category"
            @click="handleCategoryClick"
          />
        </div>
      </div>

      <!-- View All Button -->
      <div 
        class="text-center mt-12"
        data-aos="fade-up"
        data-aos-duration="600"
        data-aos-delay="600"
      >
        <button
          class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-sport-primary to-sport-accent text-white font-semibold rounded-lg hover:shadow-lg hover:scale-105 transition-all duration-300"
          @click="handleViewAllCategories"
        >
          <span>View All Categories</span>
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CategoryCard, type Category } from '@/components/examples'

// Featured categories data
const categories = ref<Category[]>([
  {
    id: 'football',
    name: 'Football',
    description: 'Professional football gear and equipment for all skill levels',
    image: 'https://images.unsplash.com/photo-1553778263-73a83bab9b0c?w=400&h=300&fit=crop',
    productCount: 156,
    isNew: false,
    salePercentage: 20
  },
  {
    id: 'basketball',
    name: 'Basketball',
    description: 'Court essentials and streetwear for basketball enthusiasts',
    image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=300&fit=crop',
    productCount: 89,
    isNew: true
  },
  {
    id: 'running',
    name: 'Running',
    description: 'Performance running shoes and apparel for every distance',
    image: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=300&fit=crop',
    productCount: 234,
    isNew: false,
    salePercentage: 15
  },
  {
    id: 'fitness',
    name: 'Fitness',
    description: 'Home and gym fitness equipment for strength training',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
    productCount: 178,
    isNew: false
  },
  {
    id: 'tennis',
    name: 'Tennis',
    description: 'Professional tennis rackets, balls, and court accessories',
    image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop',
    productCount: 67,
    isNew: true
  },
  {
    id: 'swimming',
    name: 'Swimming',
    description: 'Swimming gear, goggles, and pool accessories',
    image: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?w=400&h=300&fit=crop',
    productCount: 92,
    isNew: false,
    salePercentage: 25
  }
])

// Event handlers
const handleCategoryClick = (category: Category) => {
  console.log('🏷️ Category clicked:', category.name)
  // Navigate to category page
  // router.push(`/categories/${category.id}`)
}

const handleViewAllCategories = () => {
  console.log('📂 View all categories clicked')
  // Navigate to categories page
  // router.push('/categories')
}
</script>
