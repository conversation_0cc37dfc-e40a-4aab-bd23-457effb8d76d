<template>
  <section class="relative overflow-hidden">
    <!-- Background with gradient -->
    <div class="absolute inset-0 bg-gradient-sport dark:bg-gradient-sport-dark"></div>
    <div class="absolute inset-0 bg-black/10"></div>
    
    <div class="relative container-custom section-padding">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
        <!-- Content -->
        <div class="text-white space-y-6 animate-fade-in">
          <div>
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight">
              Elevate Your Game with 
              <span class="text-gradient-sport-animated">Premium Sports Gear</span>
            </h1>
            <p class="mt-6 text-xl lg:text-2xl opacity-90 leading-relaxed">
              Discover top-quality equipment, apparel, and accessories for every athlete and fitness enthusiast.
            </p>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 pt-4">
            <n-button 
              type="primary" 
              size="large"
              class="btn-primary-animated px-8 py-4 text-lg font-semibold"
              @click="handleShopNow"
            >
              <template #icon>
                <n-icon size="20">
                  <BagOutline />
                </n-icon>
              </template>
              Shop Now
            </n-button>
            <n-button 
              quaternary
              size="large"
              class="text-white border-white hover:bg-white/10 px-8 py-4 text-lg font-semibold"
              @click="handleExploreCategories"
            >
              <template #icon>
                <n-icon size="20">
                  <GridOutline />
                </n-icon>
              </template>
              Explore Categories
            </n-button>
          </div>

          <!-- Social Proof -->
          <div class="flex items-center gap-6 pt-6">
            <div class="flex -space-x-3">
              <div 
                v-for="i in 4" 
                :key="i"
                class="h-10 w-10 rounded-full border-2 border-white bg-gradient-to-br from-light-accent-sport to-light-accent-info"
              />
            </div>
            <div class="text-white/90">
              <div class="font-semibold text-lg">2,000+</div>
              <div class="text-sm opacity-75">satisfied customers</div>
            </div>
          </div>
        </div>

        <!-- Hero Image -->
        <div class="relative lg:h-[600px] h-[400px] animate-slide-up">
          <div class="absolute inset-0 rounded-2xl overflow-hidden shadow-2xl">
            <img
              :src="heroImage"
              alt="Premium sports equipment"
              class="w-full h-full object-cover"
              @error="handleImageError"
            />
            <!-- Overlay Badge -->
            <div class="absolute bottom-6 right-6 bg-white/90 dark:bg-black/90 backdrop-blur-sm p-4 rounded-xl shadow-lg">
              <div class="text-sm font-semibold text-light-text-primary dark:text-dark-text-primary">
                New Collection
              </div>
              <div class="text-xs text-light-text-muted dark:text-dark-text-muted">
                Summer 2025
              </div>
            </div>
          </div>
          
          <!-- Floating Elements -->
          <div class="absolute -top-4 -right-4 w-20 h-20 bg-white/20 rounded-full animate-bounce-gentle"></div>
          <div class="absolute -bottom-6 -left-6 w-16 h-16 bg-white/10 rounded-full animate-bounce-gentle" style="animation-delay: 0.5s;"></div>
        </div>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/70 animate-bounce">
      <n-icon size="24">
        <ChevronDownOutline />
      </n-icon>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import {
  BagOutline,
  GridOutline,
  ChevronDownOutline
} from '@vicons/ionicons5'

// Hero image with fallback
const heroImage = ref('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=600&fit=crop&crop=center')

// Methods
const handleShopNow = () => {
  console.log('Navigate to shop')
  // Add navigation logic here
}

const handleExploreCategories = () => {
  console.log('Navigate to categories')
  // Add navigation logic here
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = `data:image/svg+xml;base64,${btoa(`
    <svg width="600" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#00C897;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#00E5A1;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="600" height="600" fill="url(#grad)"/>
      <text x="300" y="280" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white">
        🏃‍♂️
      </text>
      <text x="300" y="340" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">
        Sport Shop
      </text>
    </svg>
  `)}`
}
</script>

<style scoped>
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}
</style>
