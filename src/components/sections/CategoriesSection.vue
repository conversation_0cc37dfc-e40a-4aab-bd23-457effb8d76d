<template>
  <section class="section-padding bg-light-bg-secondary dark:bg-dark-bg-secondary">
    <div class="container-custom">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl lg:text-4xl font-bold text-light-text-primary dark:text-dark-text-primary mb-4">
          Shop by Category
        </h2>
        <p class="text-lg text-light-text-secondary dark:text-dark-text-secondary max-w-2xl mx-auto">
          Find the perfect gear for your favorite sport. From professional equipment to casual wear, we have everything you need.
        </p>
      </div>

      <!-- Categories Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <CategoryCard
          v-for="category in categories"
          :key="category.id"
          :category="category"
          @click="handleCategoryClick"
          class="animate-fade-in"
          :style="{ animationDelay: `${categories.indexOf(category) * 0.1}s` }"
        />
      </div>

      <!-- View All Button -->
      <div class="text-center mt-12">
        <n-button 
          type="primary"
          size="large"
          class="btn-primary px-8 py-3"
          @click="handleViewAllCategories"
        >
          <template #icon>
            <n-icon size="18">
              <GridOutline />
            </n-icon>
          </template>
          View All Categories
        </n-button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { GridOutline } from '@vicons/ionicons5'
import { CategoryCard } from '@/components/examples'
import type { Category } from '@/components/examples'

// Sample categories data
const categories = ref<Category[]>([
  {
    id: 'football',
    name: 'Football',
    description: 'Professional football gear and equipment',
    image: 'https://images.unsplash.com/photo-1553778263-73a83bab9b0c?w=400&h=300&fit=crop',
    productCount: 156,
    isNew: false,
    salePercentage: 20
  },
  {
    id: 'basketball',
    name: 'Basketball',
    description: 'Court essentials and streetwear',
    image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=300&fit=crop',
    productCount: 89,
    isNew: true
  },
  {
    id: 'running',
    name: 'Running',
    description: 'Performance running shoes and apparel',
    image: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=300&fit=crop',
    productCount: 234,
    isNew: false,
    salePercentage: 15
  },
  {
    id: 'fitness',
    name: 'Fitness',
    description: 'Gym equipment and workout gear',
    image: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop',
    productCount: 178,
    isNew: false
  },
  {
    id: 'tennis',
    name: 'Tennis',
    description: 'Rackets, balls, and court apparel',
    image: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400&h=300&fit=crop',
    productCount: 67,
    isNew: false,
    salePercentage: 25
  },
  {
    id: 'swimming',
    name: 'Swimming',
    description: 'Swimwear and pool accessories',
    image: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?w=400&h=300&fit=crop',
    productCount: 45,
    isNew: true
  },
  {
    id: 'cycling',
    name: 'Cycling',
    description: 'Bikes, helmets, and cycling gear',
    image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=300&fit=crop',
    productCount: 123,
    isNew: false
  },
  {
    id: 'outdoor',
    name: 'Outdoor',
    description: 'Adventure and outdoor sports equipment',
    image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop',
    productCount: 98,
    isNew: false,
    salePercentage: 30
  }
])

// Methods
const handleCategoryClick = (category: Category) => {
  console.log('Navigate to category:', category.name)
  // Add navigation logic here
}

const handleViewAllCategories = () => {
  console.log('Navigate to all categories')
  // Add navigation logic here
}
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
